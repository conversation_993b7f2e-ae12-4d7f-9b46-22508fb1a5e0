"""
Model Inference Engine for SmallDoge WebUI
Transformers-based inference with support for multiple model types
"""

import logging
import asyncio
import torch
import gc
from typing import Dict, Any, Optional, AsyncGenerator, List
import json
import time
import threading
from pathlib import Path

from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoModel,
    GenerationConfig, TextIteratorStreamer
)
from transformers.utils import is_torch_available

from smalldoge_webui.env import SRC_LOG_LEVELS
from smalldoge_webui.constants import MODEL_TYPES, DEFAULT_MODEL_CONFIG

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["INFERENCE"])

class ModelInferenceEngine:
    """
    Model inference engine supporting transformers and external APIs
    """

    def __init__(self):
        self.loaded_models = {}
        self.model_configs = {}
        self.device = "cpu"
        self.device_map = None
        self.max_memory = None

        # Initialize device detection
        self._detect_device()

        # Thread lock for model loading
        self._model_lock = threading.Lock()

        log.info(f"ModelInferenceEngine initialized with device: {self.device}")
    
    def _detect_device(self):
        """Detect available device (CPU, CUDA, MPS) and setup memory management"""
        try:
            if is_torch_available() and torch.cuda.is_available():
                self.device = "cuda"
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3

                log.info(f"CUDA available: {gpu_count} GPU(s)")
                log.info(f"Primary GPU: {gpu_name} ({gpu_memory:.1f}GB)")

                # Setup device map for multi-GPU if available
                if gpu_count > 1:
                    self.device_map = "auto"
                    log.info("Multi-GPU setup detected, using automatic device mapping")

                # Setup memory management for limited VRAM
                if gpu_memory < 8:  # Less than 8GB VRAM
                    self.max_memory = {0: "6GB"}
                    log.info("Limited VRAM detected, setting memory constraints")

            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                self.device = "mps"
                log.info("MPS (Apple Silicon) available")
            else:
                self.device = "cpu"
                log.info("Using CPU for inference")

        except Exception as e:
            log.warning(f"Error detecting device: {e}, falling back to CPU")
            self.device = "cpu"

    async def get_model_status(self, model_id: str) -> Dict[str, Any]:
        """Get status of a specific model"""
        if model_id in self.loaded_models:
            model_info = self.loaded_models[model_id]

            # Get memory usage if available
            memory_usage = None
            if self.device == "cuda" and torch.cuda.is_available():
                memory_usage = {
                    "allocated": torch.cuda.memory_allocated() / 1024**3,
                    "cached": torch.cuda.memory_reserved() / 1024**3
                }

            return {
                "loaded": True,
                "status": model_info.get("status", "unknown"),
                "device": model_info.get("device", self.device),
                "memory_usage": memory_usage,
                "loaded_at": model_info.get("loaded_at"),
                "model_type": model_info.get("type")
            }
        else:
            return {
                "loaded": False,
                "status": "not_loaded"
            }

    async def load_model(self, model_id: str, model_config: Optional[Dict[str, Any]] = None) -> bool:
        """
        Load a model for inference

        Args:
            model_id: Unique identifier for the model
            model_config: Model configuration including type, path, etc.
                         If None, will try to load from database

        Returns:
            bool: True if model loaded successfully
        """
        try:
            # If no config provided, try to load from database
            if model_config is None:
                from smalldoge_webui.models.models import Models
                model_data = Models.get_model_by_id(model_id)
                if not model_data:
                    log.error(f"Model {model_id} not found in database")
                    return False

                model_config = {
                    "model_type": model_data.model_type,
                    "model_path": model_data.model_path,
                    "params": model_data.params or {},
                    "meta": model_data.meta or {}
                }

            model_type = model_config.get("model_type", MODEL_TYPES.TRANSFORMERS)
            model_path = model_config.get("model_path", "")

            if not model_path:
                log.error(f"No model path specified for {model_id}")
                return False

            # Check if model is already loaded
            if model_id in self.loaded_models:
                log.info(f"Model {model_id} already loaded")
                return True

            with self._model_lock:
                if model_type == MODEL_TYPES.TRANSFORMERS:
                    return await self._load_transformers_model(model_id, model_config)
                elif model_type == MODEL_TYPES.OPENAI:
                    return await self._load_openai_model(model_id, model_config)
                else:
                    log.error(f"Unsupported model type: {model_type}")
                    return False

        except Exception as e:
            log.error(f"Error loading model {model_id}: {e}")
            return False
    
    async def _load_transformers_model(self, model_id: str, config: Dict[str, Any]) -> bool:
        """Load a transformers model"""
        try:
            model_path = config.get("model_path", "")
            params = config.get("params", {})

            log.info(f"Loading transformers model {model_id} from {model_path}")

            # Load tokenizer
            log.info(f"Loading tokenizer for {model_id}")
            tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=params.get("trust_remote_code", False),
                use_fast=params.get("use_fast", True)
            )

            # Ensure tokenizer has pad token
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token

            # Load model with appropriate settings
            log.info(f"Loading model for {model_id}")

            model_kwargs = {
                "trust_remote_code": params.get("trust_remote_code", False),
                "torch_dtype": getattr(torch, params.get("torch_dtype", "float16")) if self.device != "cpu" else torch.float32,
                "device_map": self.device_map if self.device == "cuda" else None,
                "max_memory": self.max_memory,
                "low_cpu_mem_usage": True,
            }

            # Remove None values
            model_kwargs = {k: v for k, v in model_kwargs.items() if v is not None}

            # Load the model
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                **model_kwargs
            )

            # Move to device if not using device_map
            if self.device_map is None:
                model = model.to(self.device)

            # Set to eval mode
            model.eval()

            # Store model info
            self.loaded_models[model_id] = {
                "type": MODEL_TYPES.TRANSFORMERS,
                "model": model,
                "tokenizer": tokenizer,
                "config": config,
                "device": self.device,
                "loaded_at": time.time(),
                "status": "loaded"
            }

            log.info(f"Transformers model {model_id} loaded successfully on {self.device}")
            return True

        except Exception as e:
            log.error(f"Error loading transformers model {model_id}: {e}")
            # Clean up any partially loaded model
            if model_id in self.loaded_models:
                del self.loaded_models[model_id]
            return False
    
    async def _load_openai_model(self, model_id: str, config: Dict[str, Any]) -> bool:
        """Load OpenAI API model configuration"""
        try:
            self.loaded_models[model_id] = {
                "type": MODEL_TYPES.OPENAI,
                "config": config,
                "loaded_at": time.time(),
                "status": "loaded"
            }
            
            log.info(f"OpenAI model {model_id} configured successfully")
            return True
            
        except Exception as e:
            log.error(f"Error configuring OpenAI model {model_id}: {e}")
            return False
    
    async def unload_model(self, model_id: str) -> bool:
        """Unload a model from memory"""
        try:
            if model_id in self.loaded_models:
                model_info = self.loaded_models[model_id]

                # Clean up model and tokenizer
                if "model" in model_info:
                    del model_info["model"]
                if "tokenizer" in model_info:
                    del model_info["tokenizer"]

                # Remove from loaded models
                del self.loaded_models[model_id]

                # Force garbage collection
                gc.collect()

                # Clear CUDA cache if using GPU
                if self.device == "cuda" and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    log.info(f"CUDA cache cleared after unloading {model_id}")

                log.info(f"Model {model_id} unloaded successfully")
                return True
            else:
                log.warning(f"Model {model_id} not found in loaded models")
                return False

        except Exception as e:
            log.error(f"Error unloading model {model_id}: {e}")
            return False
    
    async def generate_response(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate response from model
        
        Args:
            model_id: ID of the model to use
            messages: List of chat messages
            stream: Whether to stream the response
            **kwargs: Additional generation parameters
            
        Returns:
            Dict containing the response
        """
        try:
            if model_id not in self.loaded_models:
                raise ValueError(f"Model {model_id} not loaded")
            
            model_info = self.loaded_models[model_id]
            model_type = model_info["type"]
            
            if stream:
                # For streaming, we'll return a placeholder
                return {"stream": True, "model": model_id}
            else:
                # Generate non-streaming response
                if model_type == MODEL_TYPES.TRANSFORMERS:
                    return await self._generate_transformers_response(model_id, messages, **kwargs)
                elif model_type == MODEL_TYPES.OPENAI:
                    return await self._generate_openai_response(model_id, messages, **kwargs)
                else:
                    raise ValueError(f"Unsupported model type: {model_type}")
                    
        except Exception as e:
            log.error(f"Error generating response with model {model_id}: {e}")
            raise
    
    async def _generate_transformers_response(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response using transformers model"""
        try:
            model_info = self.loaded_models[model_id]
            model = model_info["model"]
            tokenizer = model_info["tokenizer"]

            # Format messages into prompt
            prompt = self._format_messages_for_transformers(messages)

            # Tokenize input
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=kwargs.get("max_input_length", 2048)
            )

            # Move inputs to device
            inputs = {k: v.to(model.device) for k, v in inputs.items()}

            # Generation parameters
            generation_config = GenerationConfig(
                max_new_tokens=kwargs.get("max_tokens", 512),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 0.9),
                top_k=kwargs.get("top_k", 50),
                do_sample=kwargs.get("temperature", 0.7) > 0,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=kwargs.get("repetition_penalty", 1.1),
            )

            # Generate response
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    generation_config=generation_config,
                    return_dict_in_generate=True,
                    output_scores=True
                )

            # Decode response
            generated_tokens = outputs.sequences[0][inputs["input_ids"].shape[1]:]
            response_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)

            # Calculate token counts
            prompt_tokens = inputs["input_ids"].shape[1]
            completion_tokens = len(generated_tokens)

            return {
                "content": response_text.strip(),
                "finish_reason": "stop",
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": prompt_tokens + completion_tokens
            }

        except Exception as e:
            log.error(f"Error in transformers response generation: {e}")
            raise
    
    async def _generate_openai_response(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response using OpenAI API"""
        try:
            # Placeholder implementation
            # In a real implementation, this would call the OpenAI API
            
            await asyncio.sleep(0.1)  # Simulate API call
            
            response_text = f"This is a simulated OpenAI response from {model_id}"
            
            return {
                "id": f"chatcmpl-{int(time.time())}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": model_id,
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_text
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 20,
                    "total_tokens": 30
                }
            }
            
        except Exception as e:
            log.error(f"Error in OpenAI response generation: {e}")
            raise
    
    def _format_messages_for_transformers(self, messages: List[Dict[str, str]]) -> str:
        """Format chat messages for transformers model"""
        formatted_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                formatted_parts.append(f"System: {content}")
            elif role == "user":
                formatted_parts.append(f"User: {content}")
            elif role == "assistant":
                formatted_parts.append(f"Assistant: {content}")
        
        return "\n".join(formatted_parts)

    async def chat_completion(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate chat completion response

        Args:
            model_id: ID of the model to use
            messages: List of chat messages
            stream: Whether to stream the response
            **kwargs: Additional generation parameters

        Returns:
            Dict containing the response
        """
        try:
            if model_id not in self.loaded_models:
                raise ValueError(f"Model {model_id} not loaded")

            model_info = self.loaded_models[model_id]
            model_type = model_info["type"]

            if model_type == MODEL_TYPES.TRANSFORMERS:
                return await self._generate_transformers_response(model_id, messages, **kwargs)
            elif model_type == MODEL_TYPES.OPENAI:
                return await self._generate_openai_response(model_id, messages, **kwargs)
            else:
                raise ValueError(f"Unsupported model type: {model_type}")

        except Exception as e:
            log.error(f"Error in chat completion with model {model_id}: {e}")
            raise

    async def stream_chat_completion(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming chat completion"""
        try:
            if model_id not in self.loaded_models:
                raise ValueError(f"Model {model_id} not loaded")

            model_info = self.loaded_models[model_id]
            model_type = model_info["type"]

            if model_type == MODEL_TYPES.TRANSFORMERS:
                async for chunk in self._stream_transformers_response(model_id, messages, **kwargs):
                    yield chunk
            else:
                # Fallback to non-streaming for unsupported types
                response = await self.chat_completion(model_id, messages, **kwargs)
                yield {
                    "content": response.get("content", ""),
                    "finish_reason": response.get("finish_reason", "stop")
                }

        except Exception as e:
            log.error(f"Error in streaming chat completion: {e}")
            raise

    async def _stream_transformers_response(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response using transformers model"""
        try:
            model_info = self.loaded_models[model_id]
            model = model_info["model"]
            tokenizer = model_info["tokenizer"]

            # Format messages into prompt
            prompt = self._format_messages_for_transformers(messages)

            # Tokenize input
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=kwargs.get("max_input_length", 2048)
            )

            # Move inputs to device
            inputs = {k: v.to(model.device) for k, v in inputs.items()}

            # Setup streamer
            streamer = TextIteratorStreamer(
                tokenizer,
                skip_prompt=True,
                skip_special_tokens=True
            )

            # Generation parameters
            generation_kwargs = {
                **inputs,
                "max_new_tokens": kwargs.get("max_tokens", 512),
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.9),
                "top_k": kwargs.get("top_k", 50),
                "do_sample": kwargs.get("temperature", 0.7) > 0,
                "pad_token_id": tokenizer.pad_token_id,
                "eos_token_id": tokenizer.eos_token_id,
                "repetition_penalty": kwargs.get("repetition_penalty", 1.1),
                "streamer": streamer,
            }

            # Start generation in a separate thread
            generation_thread = threading.Thread(
                target=model.generate,
                kwargs=generation_kwargs
            )
            generation_thread.start()

            # Stream the response
            for new_text in streamer:
                if new_text:
                    yield {
                        "content": new_text,
                        "finish_reason": None
                    }
                    await asyncio.sleep(0.01)  # Small delay for responsiveness

            # Wait for generation to complete
            generation_thread.join()

            # Send final chunk
            yield {
                "content": "",
                "finish_reason": "stop"
            }

        except Exception as e:
            log.error(f"Error in streaming transformers response: {e}")
            raise

    async def generate_stream_response(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response"""
        try:
            if model_id not in self.loaded_models:
                raise ValueError(f"Model {model_id} not loaded")
            
            # Simulate streaming response
            response_text = f"This is a streaming response from {model_id}"
            words = response_text.split()
            
            for i, word in enumerate(words):
                chunk = {
                    "id": f"chatcmpl-{int(time.time())}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": model_id,
                    "choices": [
                        {
                            "index": 0,
                            "delta": {
                                "content": word + " " if i < len(words) - 1 else word
                            },
                            "finish_reason": None if i < len(words) - 1 else "stop"
                        }
                    ]
                }
                
                yield chunk
                await asyncio.sleep(0.1)  # Simulate streaming delay
                
        except Exception as e:
            log.error(f"Error in stream response generation: {e}")
            raise
    
    async def list_available_models(self) -> List[Dict[str, Any]]:
        """List all available models from database"""
        try:
            from smalldoge_webui.models.models import Models
            models = Models.get_models()

            model_list = []
            for model in models:
                model_info = {
                    "id": model.id,
                    "name": model.name,
                    "description": model.description,
                    "model_type": model.model_type,
                    "model_path": model.model_path,
                    "created_at": model.created_at.isoformat() if model.created_at else None,
                    "loaded": model.id in self.loaded_models
                }

                # Add status info if loaded
                if model.id in self.loaded_models:
                    status = await self.get_model_status(model.id)
                    model_info.update(status)

                model_list.append(model_info)

            return model_list

        except Exception as e:
            log.error(f"Error listing available models: {e}")
            return []

    async def create_embeddings(
        self,
        model_id: str,
        texts: List[str]
    ) -> List[List[float]]:
        """Create embeddings for input texts"""
        try:
            if model_id not in self.loaded_models:
                raise ValueError(f"Model {model_id} not loaded")

            model_info = self.loaded_models[model_id]

            if model_info["type"] != MODEL_TYPES.TRANSFORMERS:
                raise ValueError(f"Embeddings not supported for model type: {model_info['type']}")

            model = model_info["model"]
            tokenizer = model_info["tokenizer"]

            embeddings = []

            for text in texts:
                # Tokenize text
                inputs = tokenizer(
                    text,
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=512
                )

                # Move to device
                inputs = {k: v.to(model.device) for k, v in inputs.items()}

                # Get embeddings
                with torch.no_grad():
                    outputs = model(**inputs, output_hidden_states=True)
                    # Use last hidden state and mean pool
                    last_hidden_state = outputs.hidden_states[-1]
                    embedding = last_hidden_state.mean(dim=1).squeeze().cpu().numpy()
                    embeddings.append(embedding.tolist())

            return embeddings

        except Exception as e:
            log.error(f"Error creating embeddings: {e}")
            raise

    def get_loaded_models(self) -> Dict[str, Any]:
        """Get information about loaded models"""
        return {
            model_id: {
                "type": info["type"],
                "status": info["status"],
                "loaded_at": info["loaded_at"],
                "device": info.get("device", self.device)
            }
            for model_id, info in self.loaded_models.items()
        }

    async def cleanup(self):
        """Cleanup resources"""
        try:
            log.info("Cleaning up inference engine...")

            # Unload all models
            model_ids = list(self.loaded_models.keys())
            for model_id in model_ids:
                await self.unload_model(model_id)

            # Final cleanup
            gc.collect()
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()

            log.info("Inference engine cleanup completed")
        except Exception as e:
            log.error(f"Error during cleanup: {e}")

# Export the main class
__all__ = ["ModelInferenceEngine"]
